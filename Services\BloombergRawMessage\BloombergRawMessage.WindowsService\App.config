<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <connectionStrings>
    <add name="CoreConnectionString" connectionString="Data Source=005sql04.highland.aws,52155;Initial Catalog=HCM;Trusted_Connection=true;Connection Timeout=0;" providerName="System.Data.SqlClient" />
    <add name="FeedsConnectionString" connectionString="Data Source=005sql04.highland.aws,52155;Initial Catalog=DataFeeds;Trusted_Connection=true;Connection Timeout=0;" providerName="System.Data.SqlClient" />
  </connectionStrings>
  <appSettings>
    <add key="Domain" value="Development" />
    <add key="Version" value="1.0" />
    <add key="TransactionConfig" value="TradeFeedConfiguration" />
    <add key="DefaultIssuer" value="Unknown Issuer" />
    <add key="Location" value="Dallas" />
    <add key="Company" value="HoldCo" />
    <add key="ProcessSecMaster" value="true" />
    <!-- RabbitMQ Configuration (placeholder for future implementation) -->
    <add key="RabbitMQ.HostName" value="localhost" />
    <add key="RabbitMQ.Port" value="5672" />
    <add key="RabbitMQ.UserName" value="guest" />
    <add key="RabbitMQ.Password" value="guest" />
    <add key="RabbitMQ.QueueName" value="bloomberg.raw.messages" />
  </appSettings>
  <system.diagnostics>
    <trace autoflush="true">
      <listeners>
        <remove name="Default" />
        <add name="EventLogTraceListener" type="System.Diagnostics.EventLogTraceListener" initializeData="Bloomberg Raw Message Service" />
        <add name="FileTraceListener" type="System.Diagnostics.TextWriterTraceListener" initializeData="BloombergRawMessageService.log" />
      </listeners>
    </trace>
  </system.diagnostics>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
  </startup>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Practices.EnterpriseLibrary.Common" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Practices.Unity" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.5.0.0" newVersion="3.5.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Practices.ServiceLocation" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.3.0.0" newVersion="1.3.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
