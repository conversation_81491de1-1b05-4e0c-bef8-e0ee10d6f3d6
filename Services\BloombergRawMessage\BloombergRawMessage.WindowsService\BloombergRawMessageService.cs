using Microsoft.Practices.Unity;
using System;
using System.Diagnostics;
using System.ServiceProcess;
using System.Threading;
using System.Threading.Tasks;

namespace Siepe.Service.BloombergRawMessage.WindowsService
{
    public partial class BloombergRawMessageService : ServiceBase
    {
        private IUnityContainer _container;
        private MessageProcessor _messageProcessor;
        private CancellationTokenSource _cancellationTokenSource;
        private Task _serviceTask;

        public BloombergRawMessageService()
        {
            InitializeComponent();
        }

        protected override void OnStart(string[] args)
        {
            try
            {
                Trace.TraceInformation("[BBG RAW Service] Service starting...");
                StartService();
                Trace.TraceInformation("[BBG RAW Service] Service started successfully.");
            }
            catch (Exception ex)
            {
                Trace.TraceError($"[BBG RAW Service] Error starting service: {ex.Message}");
                Trace.TraceError($"[BBG RAW Service] Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        protected override void OnStop()
        {
            try
            {
                Trace.TraceInformation("[BBG RAW Service] Service stopping...");
                StopService();
                Trace.TraceInformation("[BBG RAW Service] Service stopped successfully.");
            }
            catch (Exception ex)
            {
                Trace.TraceError($"[BBG RAW Service] Error stopping service: {ex.Message}");
                Trace.TraceError($"[BBG RAW Service] Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Starts the service for console mode (debugging)
        /// </summary>
        public void StartConsole()
        {
            StartService();
        }

        /// <summary>
        /// Stops the service for console mode (debugging)
        /// </summary>
        public void StopConsole()
        {
            StopService();
        }

        private void StartService()
        {
            try
            {
                // Initialize Unity container
                _container = new UnityContainer();
                var bootstrapper = new ServiceBootstrapper();
                bootstrapper.ConfigureContainer(_container);

                // Resolve message processor
                _messageProcessor = _container.Resolve<MessageProcessor>();

                // Create cancellation token for graceful shutdown
                _cancellationTokenSource = new CancellationTokenSource();

                // Start the service task
                _serviceTask = Task.Run(() => RunServiceAsync(_cancellationTokenSource.Token));

                Trace.TraceInformation("[BBG RAW Service] Service initialization completed.");
            }
            catch (Exception ex)
            {
                Trace.TraceError($"[BBG RAW Service] Error in StartService: {ex.Message}");
                throw;
            }
        }

        private void StopService()
        {
            try
            {
                // Signal cancellation
                _cancellationTokenSource?.Cancel();

                // Stop RabbitMQ listener
                _messageProcessor?.StopRabbitMQListener();

                // Wait for service task to complete (with timeout)
                if (_serviceTask != null)
                {
                    if (!_serviceTask.Wait(TimeSpan.FromSeconds(30)))
                    {
                        Trace.TraceWarning("[BBG RAW Service] Service task did not complete within timeout period.");
                    }
                }

                // Dispose resources
                _cancellationTokenSource?.Dispose();
                _container?.Dispose();

                Trace.TraceInformation("[BBG RAW Service] Service cleanup completed.");
            }
            catch (Exception ex)
            {
                Trace.TraceError($"[BBG RAW Service] Error in StopService: {ex.Message}");
            }
        }

        private async Task RunServiceAsync(CancellationToken cancellationToken)
        {
            try
            {
                Trace.TraceInformation("[BBG RAW Service] Starting service main loop...");

                // Start RabbitMQ listener
                _messageProcessor.StartRabbitMQListener();

                // Keep the service running until cancellation is requested
                while (!cancellationToken.IsCancellationRequested)
                {
                    try
                    {
                        // Service heartbeat - log every 5 minutes to show service is alive
                        await Task.Delay(TimeSpan.FromMinutes(5), cancellationToken);
                        Trace.TraceInformation("[BBG RAW Service] Service heartbeat - running normally.");
                    }
                    catch (OperationCanceledException)
                    {
                        // Expected when service is stopping
                        break;
                    }
                }

                Trace.TraceInformation("[BBG RAW Service] Service main loop completed.");
            }
            catch (Exception ex)
            {
                Trace.TraceError($"[BBG RAW Service] Error in service main loop: {ex.Message}");
                Trace.TraceError($"[BBG RAW Service] Stack trace: {ex.StackTrace}");
                
                // In a production environment, you might want to restart the service
                // or implement retry logic here
                throw;
            }
        }

        protected override void OnPause()
        {
            try
            {
                Trace.TraceInformation("[BBG RAW Service] Service pausing...");
                // Implement pause logic if needed
                // For now, we'll just log the event
                base.OnPause();
                Trace.TraceInformation("[BBG RAW Service] Service paused.");
            }
            catch (Exception ex)
            {
                Trace.TraceError($"[BBG RAW Service] Error pausing service: {ex.Message}");
            }
        }

        protected override void OnContinue()
        {
            try
            {
                Trace.TraceInformation("[BBG RAW Service] Service continuing...");
                // Implement continue logic if needed
                // For now, we'll just log the event
                base.OnContinue();
                Trace.TraceInformation("[BBG RAW Service] Service continued.");
            }
            catch (Exception ex)
            {
                Trace.TraceError($"[BBG RAW Service] Error continuing service: {ex.Message}");
            }
        }

        protected override void OnShutdown()
        {
            try
            {
                Trace.TraceInformation("[BBG RAW Service] System shutdown detected, stopping service...");
                StopService();
            }
            catch (Exception ex)
            {
                Trace.TraceError($"[BBG RAW Service] Error during shutdown: {ex.Message}");
            }
        }
    }
}
