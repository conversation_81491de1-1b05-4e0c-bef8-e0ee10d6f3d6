using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration.Install;
using System.Linq;
using System.ServiceProcess;
using System.Threading.Tasks;

namespace Siepe.Service.BloombergRawMessage.WindowsService
{
    [RunInstaller(true)]
    public partial class ProjectInstaller : System.Configuration.Install.Installer
    {
        public ProjectInstaller()
        {
            InitializeComponent();
        }

        private void serviceInstaller1_AfterInstall(object sender, InstallEventArgs e)
        {
            // Optional: Start the service after installation
            try
            {
                using (ServiceController sc = new ServiceController(serviceInstaller1.ServiceName))
                {
                    sc.Start();
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't fail the installation
                System.Diagnostics.Trace.TraceError($"Error starting service after installation: {ex.Message}");
            }
        }

        private void serviceProcessInstaller1_AfterInstall(object sender, InstallEventArgs e)
        {
            // Optional: Additional setup after process installer completes
        }
    }
}
