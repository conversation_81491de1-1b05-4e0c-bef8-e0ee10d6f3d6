# Bloomberg Raw Message Windows Service

## Overview
This Windows Service replaces the WCF web service with a limited scope implementation that processes Bloomberg raw message data via RabbitMQ transport. The service implements only the Publish interface functionality and excludes `Calc.Execution.Fees` message handling.

## Features
- **Windows Service**: Runs as a background service
- **RabbitMQ Transport**: Placeholder implementation for RabbitMQ message consumption
- **Limited Scope**: Processes only Bloomberg trade feed XML data (excludes fee calculations)
- **Dependency Injection**: Uses Unity container with same configuration as Web project
- **Logging**: Event log and file-based logging
- **Console Mode**: Can run in console mode for debugging

## Architecture
```
RabbitMQ Queue → MessageProcessor → IRawMessageHandler.Process() → TradeFeedHandler.Process()
```

## Installation

### Prerequisites
- .NET Framework 4.7.2
- Windows Service permissions
- Database access (Core and Feeds connection strings)
- RabbitMQ server (for future implementation)

### Install Service
```cmd
# Build the project first
# Then install using InstallUtil
InstallUtil.exe Siepe.Service.BloombergRawMessage.WindowsService.exe

# Or use PowerShell
New-Service -Name "BloombergRawMessageService" -BinaryPathName "C:\Path\To\Siepe.Service.BloombergRawMessage.WindowsService.exe"
```

### Uninstall Service
```cmd
InstallUtil.exe /u Siepe.Service.BloombergRawMessage.WindowsService.exe
```

## Configuration

### App.config Settings
- **Connection Strings**: Core and Feeds database connections
- **RabbitMQ Settings**: Host, port, credentials, queue name (placeholder)
- **Application Settings**: Domain, version, transaction config

### Service Configuration
- **Service Name**: BloombergRawMessageService
- **Display Name**: Bloomberg Raw Message Service
- **Start Type**: Automatic
- **Account**: Local System

## Usage

### Console Mode (Development)
```cmd
# Run in console mode for debugging
Siepe.Service.BloombergRawMessage.WindowsService.exe
```

### Windows Service Mode
```cmd
# Start service
net start BloombergRawMessageService

# Stop service
net stop BloombergRawMessageService
```

## Logging
- **Event Log**: Application event log with source "Bloomberg Raw Message Service"
- **File Log**: BloombergRawMessageService.log in application directory
- **Trace Output**: Detailed tracing for debugging

## Dependencies
- All existing Bloomberg Raw Message dependencies
- Unity container for dependency injection
- Same business logic as Web project (IRawMessageHandler, TradeFeedHandler, etc.)

## Future Implementation
- RabbitMQ client integration
- Message queue subscription and processing
- Error handling and retry logic
- Performance monitoring

## Differences from Web Service
| Aspect | Web Service | Windows Service |
|--------|-------------|-----------------|
| Transport | WCF (HTTP/TCP/MSMQ) | RabbitMQ |
| Hosting | IIS | Windows Service |
| Scope | Full Publish + Fees | Publish only (no fees) |
| Deployment | Web deployment | Service installation |

## Troubleshooting
- Check Event Log for service errors
- Review BloombergRawMessageService.log file
- Verify database connectivity
- Ensure RabbitMQ server is accessible (when implemented)
