using Microsoft.Practices.Unity;
using Siepe.Expenses.Biz;
using Siepe.Expenses.Engine;
using Siepe.GenevaUtility;
using Siepe.Instruments.Data;
using Siepe.Instruments.Entities;
using Siepe.Instruments.Entities.Derivatives.Options;
using Siepe.PubSubUtility;
using Siepe.Service.BloombergRawMessage.Impl;
using Siepe.Service.BloombergRawMessage.Impl.TradeFeedProcessors.RawTradeProcessor;
using Siepe.Shared.DBUtility;
using Siepe.Shared.UnityUtility;
using Siepe.IssuerCreation;
using Siepe.Service.BloombergRawMessage.Impl.TradeFeedProcessors;
using System;
using System.Diagnostics;

namespace Siepe.Service.BloombergRawMessage.WindowsService
{
    public class ServiceBootstrapper
    {
        public void ConfigureContainer(IUnityContainer container)
        {
            try
            {
                Trace.TraceInformation("[BBG RAW Service] Configuring Unity container...");

                // Database connections
                container.RegisterType<IGenevaDataProvider, GenevaDataProvider>(new InjectionConstructor(new InjectionParameter("FeedsConnectionString"), new ResolvedParameter<ISqlDbAccess>("Feeds")))
                    .RegisterType<IGenevaServiceProvider, GenevaServiceProvider>()
                    .RegisterType<ISqlDbAccess, SqlDbAccess>(new InjectionConstructor(new InjectionParameter("CoreConnectionString")))
                    .RegisterType<ISqlDbAccess, SqlDbAccess>("Feeds", new InjectionConstructor(new InjectionParameter("FeedsConnectionString")));

                // Pub/Sub
                container.RegisterType<IPublicationService, PublicationService>();

                // Expenses
                container.RegisterType<IFeeDataProvider, FeeDataProvider>(new InjectionConstructor(new ResolvedParameter<ISqlDbAccess>("Feeds")));

                // Instrument type providers
                container.ForAssembly(typeof(IInstrumentProvider).Assembly, (a) =>
                {
                    a.RegisterInterfaces<IInstrumentProvider>();
                });

                // Handlers
                container
                    .RegisterType<ITradeFeedHandler, TradeFeedHandler>()
                    .RegisterType<ITradeFeedConfigurationProvider, TradeFeedConfigurationProvider>()
                    .RegisterType<IAllocationProvider, AllocationProvider>();

                // Implementation
                container.RegisterType<IRawMessageHandler, RawMessageHandler>();

                container.RegisterType<IRawTradeProcessorProvider, RawTradeProcessorProvider>()
                    .RegisterType<IRawTradeProcessor, CfdSwapProcessor>(nameof(CfdSwapProcessor),
                        new ContainerControlledLifetimeManager(), new InjectionConstructor(new ResolvedParameter<ISqlDbAccess>()));

                // Message processor
                container.RegisterType<MessageProcessor>();

                Trace.TraceInformation("[BBG RAW Service] Unity container configuration completed successfully.");
            }
            catch (Exception ex)
            {
                Trace.TraceError($"[BBG RAW Service] Error configuring Unity container: {ex.Message}");
                Trace.TraceError($"[BBG RAW Service] Stack trace: {ex.StackTrace}");
                throw;
            }
        }
    }
}
