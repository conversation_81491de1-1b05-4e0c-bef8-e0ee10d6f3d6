<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <ProductVersion>9.0.30729</ProductVersion>
    <RootNamespace>Siepe.Service.BloombergRawMessage.Impl</RootNamespace>
    <AssemblyName>Siepe.Service.BloombergRawMessage.Impl</AssemblyName>
    <TargetFramework>net472</TargetFramework>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <FileUpgradeFlags />
    <UpgradeBackupLocation />
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <AssemblyTitle>Siepe.Service.BloombergRawMessage.Impl</AssemblyTitle>
    <Company>Highland Capital Management, L.P.</Company>
    <Product>Siepe.Service.BloombergRawMessage.Impl</Product>
    <Copyright>Copyright ©  2012</Copyright>
    <Deterministic>false</Deterministic>
    <AssemblyVersion>4.1.*</AssemblyVersion>
    <FileVersion>4.1.0.0</FileVersion>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <OutputPath>bin\$(Configuration)\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugType>full</DebugType>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Development|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <CodeAnalysisLogFile>bin\Debug\Hcmlp.Service.GenevaAdapter.Impl.dll.CodeAnalysisLog.xml</CodeAnalysisLogFile>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.cs</CodeAnalysisModuleSuppressionsFile>
    <CodeAnalysisRuleSetDirectories>;C:\Program Files (x86)\Microsoft Visual Studio 10.0\Team Tools\Static Analysis Tools\\Rule Sets</CodeAnalysisRuleSetDirectories>
    <CodeAnalysisIgnoreBuiltInRuleSets>true</CodeAnalysisIgnoreBuiltInRuleSets>
    <CodeAnalysisRuleDirectories>;C:\Program Files (x86)\Microsoft Visual Studio 10.0\Team Tools\Static Analysis Tools\FxCop\\Rules</CodeAnalysisRuleDirectories>
    <CodeAnalysisIgnoreBuiltInRules>true</CodeAnalysisIgnoreBuiltInRules>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'QA|AnyCPU'">
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <CodeAnalysisLogFile>bin\Release\Hcmlp.Service.GenevaAdapter.Impl.dll.CodeAnalysisLog.xml</CodeAnalysisLogFile>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.cs</CodeAnalysisModuleSuppressionsFile>
    <CodeAnalysisRuleSetDirectories>;C:\Program Files (x86)\Microsoft Visual Studio 10.0\Team Tools\Static Analysis Tools\\Rule Sets</CodeAnalysisRuleSetDirectories>
    <CodeAnalysisIgnoreBuiltInRuleSets>true</CodeAnalysisIgnoreBuiltInRuleSets>
    <CodeAnalysisRuleDirectories>;C:\Program Files (x86)\Microsoft Visual Studio 10.0\Team Tools\Static Analysis Tools\FxCop\\Rules</CodeAnalysisRuleDirectories>
    <CodeAnalysisIgnoreBuiltInRules>true</CodeAnalysisIgnoreBuiltInRules>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Production|AnyCPU'">
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <CodeAnalysisLogFile>bin\Release\Hcmlp.Service.GenevaAdapter.Impl.dll.CodeAnalysisLog.xml</CodeAnalysisLogFile>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.cs</CodeAnalysisModuleSuppressionsFile>
    <CodeAnalysisRuleSetDirectories>;C:\Program Files (x86)\Microsoft Visual Studio 10.0\Team Tools\Static Analysis Tools\\Rule Sets</CodeAnalysisRuleSetDirectories>
    <CodeAnalysisIgnoreBuiltInRuleSets>true</CodeAnalysisIgnoreBuiltInRuleSets>
    <CodeAnalysisRuleDirectories>;C:\Program Files (x86)\Microsoft Visual Studio 10.0\Team Tools\Static Analysis Tools\FxCop\\Rules</CodeAnalysisRuleDirectories>
    <CodeAnalysisIgnoreBuiltInRules>true</CodeAnalysisIgnoreBuiltInRules>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="CommonServiceLocator" Version="1.3" />
    <PackageReference Include="EnterpriseLibrary.Caching" Version="5.0.505.0" />
    <PackageReference Include="EnterpriseLibrary.Common" Version="6.0.1304.0" />
    <PackageReference Include="Siepe.Services.PublishSubscribe.Assemblies" Version="4.5.20160825.1" />
    <PackageReference Include="Siepe.Shared.Business.Investments" Version="4.5.20160920.1" />
    <PackageReference Include="Siepe.Shared.Service.Common" Version="4.5.1" />
    <PackageReference Include="Siepe.Shared.Service.Configuration.Assemblies" Version="4.5.14353.1" />
    <PackageReference Include="Siepe.Shared.Utils" Version="4.0.2" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="7.0.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Include="System.Security.AccessControl" Version="6.0.0" />
    <PackageReference Include="System.Security.Permissions" Version="7.0.0" />
    <PackageReference Include="System.Security.Principal.Windows" Version="5.0.0" />
    <PackageReference Include="Unity" Version="3.5.1404.0" />
    <PackageReference Include="Unity.Interception" Version="3.5.1404.0" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System.configuration" />
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.Net" />
    <Reference Include="System.Security" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Transactions" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\Libraries\Allocations\Allocations.Entities\Allocations.Entities.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Allocations\Orders.Entities\Orders.Entities.csproj" />
    <ProjectReference Include="..\..\..\Libraries\DBUtility\DBUtility\DBUtility.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Expenses\Expenses.Biz\Expenses.Biz.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Expenses\Expenses.Engine\Expenses.Engine.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Expenses\Expenses.Entities\Expenses.Entities.csproj" />
    <ProjectReference Include="..\..\..\Libraries\GenevaUtility\GenevaUtility.Entities\GenevaUtility.Entities.csproj" />
    <ProjectReference Include="..\..\..\Libraries\GenevaUtility\GenevaUtility\GenevaUtility.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Instruments\Siepe.Instruments.Data\Siepe.Instruments.Data.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Instruments\Siepe.Instruments.Entities\Siepe.Instruments.Entities.csproj" />
    <ProjectReference Include="..\..\..\Libraries\IssuerCreation\IssuerCreation\IssuerCreation.csproj" />
    <ProjectReference Include="..\..\..\Libraries\PubSubUtility\PubSubUtility\PubSubUtility.csproj" />
    <ProjectReference Include="..\..\..\Libraries\RulesEngine\Siepe.RulesEngine.Domain\Siepe.RulesEngine.Domain.csproj" />
    <ProjectReference Include="..\..\..\Libraries\RulesEngine\Siepe.RulesEngine.Services\Siepe.RulesEngine.Services.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Serialization\JsonSerializer\JsonSerializer.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Serialization\Serialization\Serialization.csproj" />
    <ProjectReference Include="..\Contract\Contract.csproj" />
  </ItemGroup>
</Project>