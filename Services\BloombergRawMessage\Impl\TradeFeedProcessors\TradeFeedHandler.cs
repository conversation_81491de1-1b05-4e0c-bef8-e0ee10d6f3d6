using Microsoft.Practices.Unity;
using Siepe.Allocations.Entities;
using Siepe.Expenses.Entities;
using Siepe.GenevaUtility.Entities;
using Siepe.Orders.Entities;
using Siepe.Service.BloombergRawMessage.Contract;
using Siepe.Shared.Utils.Configuration;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Xml;
using System.Xml.Serialization;
using OrderInstrument = Siepe.Orders.Entities.Instrument;
using Instrument = Siepe.Instruments.Entities.Instrument;
using AllocationPortfolio = Siepe.Allocations.Entities.Portfolio;
using Portfolio = Siepe.Service.BloombergRawMessage.Contract.Portfolio;
using System.Data.SqlClient;
using System.Globalization;
using Siepe.Instruments.Data;
using Siepe.Expenses.Biz;
using Siepe.Expenses.Engine;
using Siepe.GenevaUtility;
using Siepe.Shared.DBUtility;
using Siepe.PubSubUtility;
using Siepe.Service.BloombergRawMessage.Impl.TradeFeedProcessors.RawTradeProcessor;

namespace Siepe.Service.BloombergRawMessage.Impl
{
    public class TradeFeedHandler : ITradeFeedHandler
    {
        [Dependency]
        public ICalculationEngine _calcEngine { get; set; }

        [Dependency]
        public IFeeDataProvider _feeProvider { get; set; }

        [Dependency]
        public ITransactionResolver _transactionResolver { get; set; }

        [Dependency]
        public IPublicationService publicationService { get; set; }

        [Dependency]
        public ITradeFeedConfigurationProvider configurationProvider { get; set; }

        [Dependency]
        public IAllocationProvider allocationProvider { get; set; }

        [Dependency]
        public IRawTradeProcessorProvider rawTradeProcessorProvider { get; set; }

        public Trade Trade { get; set; }

        public TradeFeedConfiguration Configuration { get; set; }

        public string ObjectType { get; set; } = "Trade";

        public void Process(Trade rawMessage)
        {
            Trace.TraceInformation("[BBG RAW] Processing trade message.");
            Trade = rawMessage;
            CleanTrade(Trade);
            Configuration = configurationProvider.GetConfiguration();
            ITransactionType processor = null;
            if (Configuration != null)
            {
                processor = GetTransactionTypeFromFlag();
                if (Trade.TradeDetails.BloombergFunctions.Contains("B-E "))
                {
                    Trace.TraceInformation("[BBG RAW] Trade Function B-E. Trade will not be processed.");
                    return;
                }

                if (processor != null)
                {
                    RawTrade rawTrade = BuildRawTrade();

                    if (IsBlockCreate())
                    {
                        Trace.TraceInformation("[BBG RAW] Saving Block.");
                        var blockId = SaveBlockTrade(rawTrade);
                        HandleTradeFees(blockId, rawTrade);
                    }
                    else if (IsBlockCancel()) {
                        Trace.TraceInformation("[BBG RAW] Cancelling Block.");
                        CancelBlock(rawTrade);
                    }
                    else if (IsAllocationCreate())
                    {
                        Trace.TraceInformation("[BBG RAW] Saving Allocation.");
                        foreach (var rawTradeProcessor in rawTradeProcessorProvider.GetRawTradeProcessors())
                        {
                            rawTradeProcessor.Process(rawTrade);
                        }

                        SaveRawTrade(rawTrade);
                    }
                    else if (IsAllocationCancel())
                    {
                        string tranId = GetTransactionId();
                    }
                }
            }
        }

		public RawTrade GetRawTrade(Trade rawMessage)
		{
			Trade = rawMessage;
			CleanTrade(Trade);
			if (Configuration == null)
			{
				Configuration = configurationProvider.GetConfiguration();
			}

			return BuildRawTrade();
		}

       #region raw trade

        private void CleanTrade(Trade trade)
        {
            //hard code this for now
            //if more updates come in extract to trade cleaner Interface
            if(trade.TradeDetails.RecordType == 302 || trade.TradeDetails.RecordType == 342)
            {
                switch (trade.TradeDetails.BuySellCoverShortFlag)
                {
                    case "B":
                        trade.TradeDetails.BuySellCoverShortFlag = "S";
                        break;
                    case "S":
                        trade.TradeDetails.BuySellCoverShortFlag = "B";
                        break;
                    case "H":
                        trade.TradeDetails.BuySellCoverShortFlag = "C";
                        break;
                    case "C":
                        trade.TradeDetails.BuySellCoverShortFlag = "H";
                        break;
                    default:
                        break;
                }
            }
        }

        private RawTrade BuildRawTrade()
        {
            RawTrade rawTrade = new RawTrade();
            rawTrade.EventDate = ParseDate(Trade.TradeDetails.AsOfTradeDate);
            rawTrade.SettleDate = ParseDate(Trade.TradeDetails.SettlementDate);
            rawTrade.ActualSettleDate = ParseDate(Trade.TradeDetails.SettlementDate);
            rawTrade.Investment = Trade.TradeDetails.UniqueBloombergID;
            rawTrade.BloombergID = Trade.TradeDetails.UniqueBloombergID;
            rawTrade.Portfolio = Trade.TradeDetails.TraderAccountName;
            rawTrade.LocationAccount = Trade.TradeDetails.PrimeBroker;
            rawTrade.Quantity = GetQuantity();
            rawTrade.Price = Convert.ToDecimal(Trade.TradeDetails.ExtendedPrecisionPrice);
            rawTrade.NetInvestmentAmount = GetNetInvestmentAmount();
            rawTrade.SecFeeAmount = GetSecFeeAmount();
            rawTrade.PriceDenomination = Trade.TradeDetails.SecurityCurrencyISOCode;
            rawTrade.CounterInvestment = Trade.TradeDetails.SettlementCurrencyISOCode;
            rawTrade.TotCommission = GetCommission();
            rawTrade.Broker = Trade.TradeDetails.CustomerAccountCounterparty;
            rawTrade.UserTranId1 = GetTicketPrefix() + Trade.TradeDetails.TransactionNumber;
            rawTrade.BloombergReferenceNumber = Trade.TradeDetails.BloombergReferenceNumber;
            rawTrade.ParentExecutionID = GetTicketPrefix() + Trade.TradeDetails.MasterTicketNumber;
            rawTrade.OrderID = Trade.TradeDetails.OMSOrderNumber;
            rawTrade.Trader = Trade.TradeDetails.SalespersonLogin;
            rawTrade.SettleLocation = Trade.TradeDetails.SettlementLocationAbbreviation;
            rawTrade.TradeFXRate = Convert.ToDecimal(Trade.TradeDetails.SettlementCurrencyRate);
            rawTrade.CounterFXDenomination = Trade.TradeDetails.SettlementCurrencyISOCode;
            rawTrade.SideCode = GetSideCode();
            rawTrade.MessageType = GetMessageType();
            rawTrade.GrossAmountLocal = Convert.ToDecimal(Trade.TradeDetails.PrincipalLoanAmount);
            rawTrade.GrossAmountRC = Convert.ToDecimal(Trade.TradeDetails.SettlementCcyPrincipal);
            rawTrade.AccruedInterest = Convert.ToDecimal(Trade.TradeDetails.AccruedInterestRepoInterest);
            rawTrade.CfdSwapFlag = Trade.TradeDetails.IsCfd;
            rawTrade.NetAmountLocal = Convert.ToDecimal(Trade.TradeDetails.TotalTradeAmount);
            rawTrade.NetAmountSettled = Convert.ToDecimal(Trade.TradeDetails.SettlementAmount);
            rawTrade.MortgageFactor = Convert.ToDecimal(Trade.TradeDetails.MortgageBradyIndexBondFactor);
            rawTrade.ContractualSettleDate = ParseDate(Trade.TradeDetails.WorkoutDate);
            rawTrade.Strategy = GetStrategy();
            rawTrade.PrimaryExchangeMIC = GetPrimaryExchange();
            rawTrade.RecordType = Trade.TradeDetails.RecordType;
            rawTrade.TradeFlatFlag = Trade.TradeDetails.TradeFlatFlag;
            rawTrade.OriginalXml = WriteOriginalXml();
            return rawTrade;
        }

        private void SaveRawTrade(RawTrade rawTrade)
        {
            Trace.TraceInformation("[BBG RAW] Attempting to save raw trade message.");
            try
            {
                allocationProvider.SaveAllocation(rawTrade);
            }
            catch (Exception e)
            {
                Trace.TraceError($"[BBG RAW] Unable to save raw trade message: {e.Message}");
            }
        }
        private long SaveBlockTrade(RawTrade rawTrade)
        {
            Trace.TraceInformation("[BBG RAW] Attempting to save block trade message.");
            try
            {
                 return allocationProvider.SaveBlock(rawTrade);
            }
            catch (Exception e)
            {
                Trace.TraceError($"[BBG RAW] Unable to save block trade message: {e.Message}");
                throw e;
            }
        }

        private void CancelBlock(RawTrade rawTrade)
        {
            allocationProvider.CancelBlock(rawTrade);
        }

        private string WriteOriginalXml()
        {
            string xml = "";
            using (StringWriter stream = new StringWriter())
            {
                XmlWriterSettings settings = new XmlWriterSettings() { OmitXmlDeclaration = true };
                using (XmlWriter writer = XmlWriter.Create(stream, settings))
                {
                    XmlSerializer serializer = new XmlSerializer(typeof(Trade));
                    serializer.Serialize(writer, Trade);
                    xml = stream.ToString();
                }
            }
            return xml;
        }

        #endregion

        #region trade fee

        public void HandleTradeFees(long executionId, RawTrade rawTrade)
        {
            Trace.TraceInformation("[BBG RAW] Calculating trade fees.");
            Allocation allocation = allocationProvider.BuildAllocation(rawTrade);
            List<AppliedExpense> expenses = GetExpenses(allocation);
            if (expenses.Count > 0)
            {
                Trace.TraceInformation("[BBG RAW] Saving trade fees to database.");
                _feeProvider.SaveExpenses(executionId, expenses);
            }
            else
            {
                Trace.TraceInformation("[BBG RAW] No trade fees exist for this trade.");
            }
        }

        private List<AppliedExpense> GetExpenses(Allocation allocation)
        {
            List<AppliedExpense> allExpenses = new List<AppliedExpense>();
            List<AppliedExpense> executionExpenses = _calcEngine.CalculateExpenses(allocation.Execution);
            allExpenses.AddRange(executionExpenses);
            return allExpenses;
        }

        #endregion

        #region transaction type

        private ITransactionType GetTransactionTypeFromFlag()
        {
            if (Configuration.TransactionTypes.Count > 0)
            {
                var mapping = Configuration.TransactionTypes.FirstOrDefault(m => m.Raw.ToUpper().Equals(Trade.TradeDetails?.BuySellCoverShortFlag?.ToUpper()));
                if (mapping != null)
                {
                    string typeName = mapping.TransactionType;
                    Trace.TraceInformation($"[BBG RAW] Trade message type: {typeName}");
                    return _transactionResolver.Resolve(typeName);
                }
            }
            return null;
        }

        private bool IsAllocationCreate()
        {
            return Configuration.CreateRecordTypes != null
                && Configuration.CreateRecordTypes.Any(type => Trade.TradeDetails?.RecordType != null && type.ToString().Equals(Trade.TradeDetails.RecordType.ToString()));
        }

        private bool IsAllocationCancel()
        {
            return Configuration.CancelRecordTypes != null
                && Configuration.CancelRecordTypes.Any(type => Trade.TradeDetails?.RecordType != null && type.ToString().Equals(Trade.TradeDetails.RecordType.ToString()));
        }

        private bool IsBlockCreate()
        {
            return Configuration.BlockCreateRecordTypes?.Any(t => t == Trade.TradeDetails?.RecordType) ?? false;
        }
        private bool IsBlockCancel()
        {
            return Configuration.BlockCancelRecordTypes?.Any(t => t == Trade.TradeDetails?.RecordType) ?? false;
        }
        #endregion

        #region helpers

        private DateTime? ParseDate(string date)
        {
            if (DateTime.TryParseExact(date, "yyyy-MM-dd", null, DateTimeStyles.None, out DateTime d))
            {
                return d;
            }
            else if (DateTime.TryParse(date, out DateTime r))
            {
                return r.Date;
            }
            return null;
        }

        private decimal GetQuantity()
        {
            decimal quantity = 0;
            decimal contractSize = 1;
            if (decimal.TryParse(Trade.TradeDetails.TradeAmount, out decimal q))
            {
                quantity = q;
            }
            if (decimal.TryParse(Trade.TradeDetails.ContractSize, out decimal c) && c != 0)
            {
                contractSize = c;
            }
            return quantity / contractSize;
        }

        private decimal GetCommission()
        {
            decimal commission = 0m;
            if (Trade.TransactionCosts.Any(cost => cost.Type.Equals("2")))
            {
                commission = Convert.ToDecimal(Trade.TransactionCosts.First(cost => cost.Type.Equals("2")).Cost);
            }
            return commission;
        }

        private string GetTicketPrefix()
        {
            if (Configuration.FieldMappings.Any(mapping => mapping.Key.Equals("TicketPrefix")))
            {
                return Configuration.FieldMappings.First(mapping => mapping.Key.Equals("TicketPrefix")).Value;
            }
            return "";
        }

        private string GetNetInvestmentAmount()
        {
            if (Configuration.FieldMappings.Any(mapping => mapping.Key.Equals("NetInvestmentAmount")))
            {
                return Configuration.FieldMappings.First(mapping => mapping.Key.Equals("NetInvestmentAmount")).Value;
            }
            return "";
        }

        private string GetSideCode()
        {
            if (Configuration.TransactionTypes.Any(type => type.Raw.Equals(Trade.TradeDetails.BuySellCoverShortFlag)))
            {
                return Configuration.TransactionTypes.First(type => type.Raw.Equals(Trade.TradeDetails.BuySellCoverShortFlag)).Flag;
            }
            return "";
        }

        private string GetMessageType()
        {
            if (Configuration.TransactionTypes.Any(type => type.Raw.Equals(Trade.TradeDetails.BuySellCoverShortFlag)))
            {
                TransactionTypeConfiguration config = Configuration.TransactionTypes.First(type => type.Raw.Equals(Trade.TradeDetails.BuySellCoverShortFlag));
                if (IsAllocationCreate() || IsBlockCreate())
                {
                    return config.CreateMessageType;
                }
                else if (IsAllocationCancel() || IsBlockCancel())
                {
                    return config.CancelMessageType;
                }
            }
            return "";
        }

        private decimal GetSecFeeAmount()
        {
            if (Trade.TransactionCosts != null && Trade.TransactionCosts.Any(cost => !string.IsNullOrWhiteSpace(cost.Code) && cost.Code.Equals("SEC")))
            {
                return Convert.ToDecimal(Trade.TransactionCosts.First(cost => !string.IsNullOrWhiteSpace(cost.Code) && cost.Code.Equals("SEC")).Cost);
            }
            return 0m;
        }

        private string GetPrimaryExchange()
        {
            if (Trade.TradeDetails.ShortNotes != null && Trade.TradeDetails.ShortNotes.Any(note => note.Index == 5))
            {
                return Trade.TradeDetails.ShortNotes.First(note => note.Index == 5).Text;
            }
            return "";
        }

        private string GetStrategy()
        {
            if (Trade.TradeDetails.StrategyTags != null && Trade.TradeDetails.StrategyTags.Any(tag => tag.Level == 1))
            {
                return Trade.TradeDetails.StrategyTags.First(tag => tag.Level == 1).Name;
            }
            return "";
        }

        private string GetTransactionId()
        {
            string prefix = "";
            if (Configuration.FieldMappings.Any(m => m.Key.Equals("TicketPrefix")))
            {
                prefix = Configuration.FieldMappings.First(m => m.Key.Equals("TicketPrefix")).Value;
            }
            return prefix + Trade.TradeDetails.OriginalTktId;
        }

        #endregion
    }
}
